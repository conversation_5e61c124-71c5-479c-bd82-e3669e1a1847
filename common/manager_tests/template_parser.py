import logging
import re
from typing import Dict, List, Optional, Tuple
from database import MicrotopicRepository, SubjectRepository

logger = logging.getLogger(__name__)


class HomeworkTemplateParser:
    """Парсер шаблонов домашних заданий"""
    
    def __init__(self):
        self.valid_letters = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]
    
    async def parse_template(self, template_text: str, subject_id: int) -> Tuple[bool, Dict, List[str]]:
        """
        Парсинг шаблона домашнего задания
        
        Args:
            template_text: Текст шаблона
            subject_id: ID предмета для проверки микротем
            
        Returns:
            Tuple[bool, Dict, List[str]]: (успех, данные, список ошибок)
        """
        errors = []
        result = {
            "test_name": "",
            "questions": []
        }
        
        try:
            # Разбиваем текст на строки
            lines = [line.strip() for line in template_text.strip().split('\n')]
            
            # Парсим название ДЗ
            test_name = self._parse_test_name(lines, errors)
            if test_name:
                result["test_name"] = test_name
            
            # Парсим вопросы
            questions = await self._parse_questions(lines, subject_id, errors)
            result["questions"] = questions
            
            # Проверяем общую валидность
            if not result["test_name"]:
                errors.append("❌ Не указано название ДЗ")
            
            if not result["questions"]:
                errors.append("❌ Не найдено ни одного вопроса")
            
            return len(errors) == 0, result, errors
            
        except Exception as e:
            logger.error(f"Ошибка при парсинге шаблона: {e}")
            errors.append(f"❌ Ошибка парсинга: {str(e)}")
            return False, result, errors
    
    def _parse_test_name(self, lines: List[str], errors: List[str]) -> Optional[str]:
        """Парсинг названия ДЗ"""
        for line in lines:
            if line.startswith("Название ДЗ:"):
                name = line.replace("Название ДЗ:", "").strip()
                if not name:
                    errors.append("❌ Название ДЗ не может быть пустым")
                    return None
                return name
        
        errors.append("❌ Не найдено поле 'Название ДЗ:'")
        return None
    
    async def _parse_questions(self, lines: List[str], subject_id: int, errors: List[str]) -> List[Dict]:
        """Парсинг вопросов"""
        questions = []
        current_question = {}
        current_variants = []
        question_number = 0
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            if line.startswith("Вопрос:"):
                # Сохраняем предыдущий вопрос если есть
                if current_question.get("text"):
                    question_data = await self._finalize_question(
                        current_question, current_variants, subject_id, question_number, errors
                    )
                    if question_data:
                        questions.append(question_data)
                
                # Начинаем новый вопрос
                question_number += 1
                current_question = {"text": line.replace("Вопрос:", "").strip()}
                current_variants = []
                
                if not current_question["text"]:
                    errors.append(f"❌ Вопрос {question_number}: текст вопроса не может быть пустым")
            
            elif line.startswith("Вариант:"):
                variant_text = line.replace("Вариант:", "").strip()
                if variant_text:
                    current_variants.append(variant_text)
                else:
                    errors.append(f"❌ Вопрос {question_number}: пустой вариант ответа")
            
            elif line.startswith("Микротема:"):
                try:
                    microtopic_num = int(line.replace("Микротема:", "").strip())
                    current_question["microtopic_number"] = microtopic_num
                except ValueError:
                    errors.append(f"❌ Вопрос {question_number}: некорректный номер микротемы")
            
            elif line.startswith("Правильный ответ на вопрос:"):
                answer = line.replace("Правильный ответ на вопрос:", "").strip().upper()
                current_question["correct_answer"] = answer
            
            elif line.startswith("Время ответа на вопрос:"):
                time_text = line.replace("Время ответа на вопрос:", "").strip()
                try:
                    time_limit = int(time_text)
                    if time_limit < 10 or time_limit > 600:
                        errors.append(f"❌ Вопрос {question_number}: время должно быть от 10 до 600 секунд")
                    else:
                        current_question["time_limit"] = time_limit
                except ValueError:
                    errors.append(f"❌ Вопрос {question_number}: некорректное время ответа")
            
            elif line.startswith("Возможность добавить фото к вопросу:"):
                photo_flag = line.replace("Возможность добавить фото к вопросу:", "").strip()
                current_question["needs_photo"] = photo_flag == "+"
            
            i += 1
        
        # Обрабатываем последний вопрос
        if current_question.get("text"):
            question_data = await self._finalize_question(
                current_question, current_variants, subject_id, question_number, errors
            )
            if question_data:
                questions.append(question_data)
        
        return questions
    
    async def _finalize_question(self, question: Dict, variants: List[str], 
                                subject_id: int, question_num: int, errors: List[str]) -> Optional[Dict]:
        """Финализация и валидация вопроса"""
        
        # Проверяем количество вариантов
        if len(variants) < 2:
            errors.append(f"❌ Вопрос {question_num}: минимум 2 варианта ответа")
            return None
        
        # Присваиваем буквы вариантам
        options = {}
        for i, variant in enumerate(variants):
            if i < len(self.valid_letters):
                options[self.valid_letters[i]] = variant
        
        question["options"] = options
        
        # Проверяем правильный ответ
        correct_answer = question.get("correct_answer", "")
        if not correct_answer or correct_answer not in options:
            available_letters = ", ".join(list(options.keys()))
            errors.append(f"❌ Вопрос {question_num}: правильный ответ должен быть одной из букв: {available_letters}")
            return None
        
        # Проверяем микротему
        microtopic_number = question.get("microtopic_number")
        if microtopic_number is None:
            errors.append(f"❌ Вопрос {question_num}: не указан номер микротемы")
            return None
        
        # Проверяем существование микротемы в БД
        microtopic = await MicrotopicRepository.get_by_number(subject_id, microtopic_number)
        if not microtopic:
            errors.append(f"❌ Вопрос {question_num}: микротема №{microtopic_number} не найдена в базе данных")
            return None
        
        question["microtopic_id"] = microtopic.id
        question["microtopic_name"] = microtopic.name
        
        # Устанавливаем время по умолчанию если не указано
        if "time_limit" not in question:
            question["time_limit"] = 30
        
        # Устанавливаем флаг фото по умолчанию если не указан
        if "needs_photo" not in question:
            question["needs_photo"] = False
        
        return question


async def parse_homework_template(template_text: str, subject_id: int) -> Tuple[bool, Dict, List[str]]:
    """
    Функция-обертка для парсинга шаблона
    
    Args:
        template_text: Текст шаблона
        subject_id: ID предмета
        
    Returns:
        Tuple[bool, Dict, List[str]]: (успех, данные, список ошибок)
    """
    parser = HomeworkTemplateParser()
    return await parser.parse_template(template_text, subject_id)
